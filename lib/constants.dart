import 'package:flutter/material.dart';
import 'package:guests/ok_colors.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';

const kDefaultItemName = '銷售商品';
const kSettingsFromDropbox =
    'https://www.dropbox.com/s/a0ak7dndolakl2r/settings.json?dl=1';
const kSettingsFromGoogle =
    'https://drive.google.com/uc?export=download&id=1IgRZsReMYSW8QEjy6TEiPwCFGJ7lUsY0';
const kSettingsUrl = kSettingsFromDropbox;
const kTaxRateNormal = 0.05;
const kTaxRateFree = 0.0;
const kDefaultPadding = 12.0;
const kButtonHeight = 46.0;
const kItemHeight = 40.0;
const kPadding = 20.0;
const kAvatarSize = kRadius * 2.0;
const kIconRadius = 37.0;
const kBottomPadding = 84.0;
const kProduct = false;
const kIconSize = kIconRadius * 2.0;
const kPrimaryGradient = const LinearGradient(
  begin: const Alignment(-1.0, 0.0),
  end: const Alignment(1.0, 0.0),
  colors: const [
    OkColors.primary,
    OkColors.secondary,
  ],
  stops: const [0.0, 1.0],
);
final kLogger = Logger(
  printer: PrettyPrinter(
    methodCount: 2, // number of method calls to be displayed
    errorMethodCount: 8, // number of method calls if stacktrace is provided
    lineLength: 120, // width of the output
    colors: true, // Colorful log messages
    printEmojis: true, // Print an emoji for each log message
    printTime: true, // Should each log print contain a timestamp
  ),
);
const kDefaultInsetPadding = EdgeInsets.symmetric(
  vertical: 24.0,
  horizontal: 40.0,
);
const kContentPadding = EdgeInsets.symmetric(
  horizontal: kPadding,
);
const kChipPadding = EdgeInsets.symmetric(
  vertical: 4.0,
  horizontal: 8.0,
);
const kRadius = 30.0;
const kRadiusCircular = const Radius.circular(kRadius);
const kBorderRadius = const BorderRadius.all(kRadiusCircular);
const kTopRadius = const BorderRadius.vertical(
  top: kRadiusCircular,
);
final kNumFormat = NumberFormat("#,##0", "en_US");
final kDateFormat = DateFormat("yyyy/MM/dd");
final kDateTimeFormat = DateFormat("yyyy/MM/dd HH:mm:ss");
// final kDateTimeFormat = DateFormat('yMd');
// final kDateTimeFormat = DateFormat.yMd().add_Hms();
final kDateTimeFormatMdHm = DateFormat("MM/dd HH:mm");
// const kAESKey = '1C020EBCDFC61E624D7EAE12F146D750'; // okshop
const kAESKey = 'C30F44D5C43C4ACA5BC83099C09E9972'; // omos
const kIv = 'Dt8lyToo17X/XkXaQvihuA==';
final kYearMonth = DateFormat("yyyyMM");
const kNumToChar = const [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
];
final kCharMax = kNumToChar.length;
const kPosBAN = '83193989';
const kCountInvoice = 8;
const kPatternInvoice = r'^\d{8}$';
final kRegExpInvoice = RegExp(kPatternInvoice);
const kLimit = 50;

class Constants {
  static const designWidth = 375.0;
  static const designHeight = 667.0;
  static const designWidthRatio = 100.0 / Constants.designWidth;
  static const designHeightRatio = 100.0 / Constants.designHeight;
  static const paddingVertical = 8.0;
  static const paddingHorizontal = 20.0;
  static const buttonHeight = kButtonHeight;
  static const paddingChip = kChipPadding;
  static const paddingContent = kContentPadding;
}
