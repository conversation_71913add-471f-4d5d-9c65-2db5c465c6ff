import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:guests/app/providers/box_provider.dart';
import 'package:guests/enums.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:guests/app/models/order_model.dart';
import 'package:guests/app/providers/order_provider.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';

class TransactionsController extends GetxController
    with StateMixin<String>, ScrollMixin {
  final OrderProvider orderProvider;
  BoxProvider get boxProvider => orderProvider.boxProvider;
  final _filter = Filter().obs;
  Filter get filter => _filter.value;
  final _disposable = Completer();
  final _fetching = Completer().obs;
  final _cached = <Order>[].obs;
  Iterable<Order> get orders => _cached;
  DateTimeRange get today {
    final now = DateTime.now();
    final start = DateTime(
      now.year,
      now.month,
      now.day,
    );
    return DateTimeRange(
      start: start,
      end: start.add(1.days),
    );
  }

  TransactionsController({
    required this.orderProvider,
  });

  void _initObservable() {
    // 每秒更新事件
    final interval = Stream.periodic(1.seconds).asBroadcastStream();
    // 監聽日期變更
    interval
        .map((event) {
          // today 的時間戳
          final now = DateTime.now();
          return DateTime(
            now.year,
            now.month,
            now.day,
          ).millisecondsSinceEpoch;
        })
        .distinct()
        .skip(1)
        .where((event) => filter.dateTimeRange != null)
        .tap((event) {
          kLogger.d('[TransactionsController] today changed');
          filter.dateTimeRange = today;
        })
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
    // 監聽 mem，有變動就同步到 list
    boxProvider
        .getGsBox(BoxType.order)
        .watch()
        .debounce(300.milliseconds)
        .takeUntil(_disposable.future)
        .listen((event) {
      final box = boxProvider.getGsBox(BoxType.order);
      final list = List<Map>.from(box.getValues(), growable: false)
          .map((e) => Order.fromJson(Map<String, dynamic>.from(e)))
          .toList(growable: false);
      kLogger.d(
          '[TransactionsController] order mem changed, length(${list.length})');
      list.sort((x, y) => (y.id ?? 0).compareTo(x.id ?? 0));
      _cached.clear();
      _cached.addAll(list);
      _updateStatus();
    });
    // 監聽 db，有變動就同步到 mem
    boxProvider.getLazyBox(BoxType.order).then((box) {
      box
          .watch()
          .debounceBuffer(1.seconds)
          .takeUntil(_disposable.future)
          .listen((event) async {
        kLogger.d(
            '[TransactionsController] order db changed, length(${event.length})');
        final orders = orderProvider.getOrdersWithIds(event.map((e) => e.key));
        final box = boxProvider.getGsBox(BoxType.order);
        final latestKey = num.tryParse(box.latestKey) ?? 0;
        await for (var order in orders) {
          final acceptedId = order.id! > latestKey || box.hasData('${order.id}');
          if (acceptedId && filter.validate(order)) {
            box.write('${order.id}', order.toJson());
          }
        }
      });
    });
    // 監聽過濾器，有變動就顯示讀取中並抓取新資料
    _filter.stream
        .tap((e) => change('', status: RxStatus.loading()))
        .debounce(700.milliseconds)
        // .asyncMap((event) => onRefresh())
        .asyncMapSample((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onInit() {
    super.onInit();
    _initObservable();
  }

  @override
  void onReady() {
    filter.dateTimeRange = today;
    refreshFilter();
  }

  @override
  void onClose() {
    _fetching.value.complete();
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      filter.hasMore = false;
      filter.page = 1;
      kLogger.d(
          '[TransactionsController] onRefresh: begin get orders from db, page(${filter.page})');
      _fetching.value.complete();
      _fetching.value = Completer();
      final box = boxProvider.getGsBox(BoxType.order);
      box.erase();
      var count = 0;
      orderProvider
          .getOrderStreamFromLocalStorage(filter)
          .takeUntil(_fetching.value.future)
          .listen(
        (event) {
          box.write('${event.id}', event.toJson());
          count++;
        },
        onDone: () {
          kLogger.d(
              '[TransactionsController] onRefresh: end get orders from db, length($count)');
          // 判斷是否有更多資料
          filter.hasMore = count >= filter.limit;
        },
      );
      await 500.milliseconds.delay();
      _updateStatus();
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  void _updateStatus() {
    final status = orders.isNotEmpty ? RxStatus.success() : RxStatus.empty();
    change('', status: status);
  }

  @override
  Future<void> onEndScroll() async {
    kLogger.d('[TransactionsController] onEndScroll');
    if (filter.hasMore) {
      filter.hasMore = false;
      filter.page++;
      kLogger.d(
          '[TransactionsController] onEndScroll: begin get orders from db, page(${filter.page})');
      // 疊加資料
      var count = 0;
      final box = boxProvider.getGsBox(BoxType.order);
      final orderStream = orderProvider.getOrderStreamFromLocalStorage(filter);
      await for (var order in orderStream) {
        box.write('${order.id}', order.toJson());
        count++;
      }
      // final list = await orderStream.toList();
      kLogger.d(
          '[TransactionsController] onEndScroll: end get orders from db, length($count)');
      // 判斷是否有更多資料
      filter.hasMore = count >= filter.limit;
    } else {
      kLogger.d('[TransactionsController] onEndScroll: no more data');
    }
  }

  @override
  Future<void> onTopScroll() async {
    kLogger.d('[TransactionsController] onTopScroll');
  }

  void refreshFilter() {
    _filter.refresh();
  }
}
