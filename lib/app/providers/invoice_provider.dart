import 'dart:convert';
import 'dart:math';

import 'package:dio/dio.dart' show Dio, DioException, Headers;
import 'package:guests/app/models/invoice_created_model.dart';
import 'package:guests/app/models/invoice_creating_model.dart';
import 'package:guests/app/models/invoice_editing_model.dart';
import 'package:guests/app/models/invoice_status_req.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';
import 'package:guests/enums.dart';
import 'package:logger/logger.dart';
import 'package:xml/xml.dart';

// const authority_dev = 'https://webtest.bpscm.com.tw/SCMWEBAPI/API';
// const authority_base = 'https://www.bpscm.com.tw/SCMWebAPI/api';
// const authority = kProduct ? authority_base : authority_dev;

class InvoiceProvider {
  final Dio dio;
  final Logger logger;
  final String apiKey;
  final String posBAN;
  final String baseUrl;
  final String soapUrl;

  const InvoiceProvider({
    required this.dio,
    required this.logger,
    required this.apiKey,
    required this.posBAN,
    required this.baseUrl,
    required this.soapUrl,
  });

  static String _getSoapXmlString({
    required String key,
    required num year,
    required num month,
    required String main, // 總公司統編
    required String seller,
    String machineNo = 'M1', // 機台號碼
    num invoiceType = 35,
  }) {
    return '''
    <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <GetInvoiceRange xmlns="http://tempuri.org/">
            <machineNo>$machineNo</machineNo>
            <APIKey>$key</APIKey>
            <deptCode></deptCode>
            <invoiceYear>$year</invoiceYear>
            <MainBAN>$main</MainBAN>
            <invoiceMonth>$month</invoiceMonth>
            <sellerBAN>$seller</sellerBAN>
            <invoiceType>$invoiceType</invoiceType>
        </GetInvoiceRange>
    </soap:Body>
    </soap:Envelope>
    ''';
  }

  Future<Iterable<InvoiceCreated>> editInvoice({
    required String seller,
    String? orderNo,
    required String invoiceNo,
    required String invoiceDate,
    required String cancelDate,
    String? buyer,
  }) async {
    final req = InvoiceEditing(
      posBan: this.posBAN,
      apiKey: this.apiKey,
      state: 2,
      sellerBan: seller,
      storeCode: '',
      storeName: '',
      registerCode: '',
      orderNo: orderNo ?? '',
      invoiceNo: invoiceNo,
      invoiceDate: invoiceDate,
      // allowanceDate: '',
      cancelDate: cancelDate,
      buyerBan: buyer ?? '',
    );
    final data = jsonEncode([req.toJson()]);
    final uri = Uri.parse('$baseUrl/PosInvoiceEdits');
    // final uri = Uri.http('61.57.230.103', 'SCMWebAPITest/api/PosInvoiceEdits');
    this.dio.options.contentType = Headers.jsonContentType;
    final res = await dio.postUri<String>(
      uri,
      data: data,
    );
    final responseData = res.data;
    if (responseData != null) {
      final ls = jsonDecode(responseData);
      return List.from(ls).map((e) => InvoiceCreated.fromJson(e));
    }
    return <InvoiceCreated>[];
  }

  // Future _editInvoice(InvoiceEditing value) {
  //   value.state = 2;
  //   value.apiKey ??= kApiKey;
  //   value.posBAN ??= kPosBAN;
  //   value.storeCode ??= ''; // 一定要填入的資料
  //   // value.storeName = '';
  //   // value.registerCode = '';
  //   value.sellerBAN ??= kPosBAN;
  //   final data = jsonEncode([value.toJson()]);
  //   final uri = Uri.http('61.57.230.103', 'SCMWebAPITest/api/PosInvoiceEdits');
  //   return this.dio.postUri<String>(uri, data: data).then(
  //     (value) {
  //       kLogger.d('');
  //     },
  //     onError: (error) {
  //       kLogger.e(error);
  //       return Future<Iterable<InvoiceCreated>>.error(error);
  //     },
  //   );
  // }

  Future<Iterable<InvoiceCreated>> pushInvoice({
    required String invoiceNo,
    required String seller,
    String? buyer,
    required String randomNumber,
    String? orderNo,
    required String invoiceDate,
    String? itemName,
    required num taxType,
    required num price,
  }) {
    buyer ??= '';
    final taxRateNormal = buyer.isNotEmpty ? kTaxRateNormal : 0.0;
    var taxRate = taxType == 1 ? taxRateNormal : kTaxRateFree;
    final untaxPrice = price / (1.0 + taxRate);
    final untaxPriceRound = untaxPrice.round(); // 3600 = 3780 / 1.05
    final taxAmt = (untaxPrice * taxRate).round(); // 180 = 3600 * 0.05
    taxRate = taxType == 1 ? kTaxRateNormal : kTaxRateFree;
    num salesAmt = 0; // 應稅
    if (1 == taxType) {
      salesAmt = untaxPriceRound; // 3600 = 3780 - 180
    }
    num freeTaxSalesAmt = 0; // 免稅
    if (3 == taxType) {
      freeTaxSalesAmt = untaxPriceRound;
    }
    final zeroTaxSalesAmt = 0; // 零稅
    final invoiceType = buyer.isNotEmpty ? 25 : 35; // 有買方統編，格式25
    final req = InvoiceCreating(
      posBan: this.posBAN,
      apiKey: this.apiKey,
      state: 1, // 1: 發票開立, 4: 折讓開立
      sellerBan: seller, // 賣方統編
      storeCode: '',
      storeName: '',
      registerCode: '',
      orderNo: orderNo ?? '',
      invoiceNo: invoiceNo,
      invoiceDate: invoiceDate,
      // allowanceDate: '',
      buyerBan: buyer,
      printMark: 'Y',
      // memberId: '',
      // checkNo: '',
      // invoiceType: '35',
      invoiceType: '$invoiceType',
      // groupMark: '',
      salesAmt: salesAmt, // 3,600
      freeTaxSalesAmt: freeTaxSalesAmt,
      zeroTaxSalesAmt: zeroTaxSalesAmt,
      taxAmt: taxAmt, // 180
      totalAmt: price, // 3,780
      taxType: '$taxType',
      taxRate: taxRate,
      discountAmt: 0,
      healthyAmt: 0,
      // carrierType: '',
      // carrierId1: '',
      // carrierId2: '',
      // npoBan: '',
      randomNumber: randomNumber,
      // mainRemark: '',
      // formatType: '',
    );
    final item = InvoiceDetail(
      sequenceNo: '',
      itemName: (itemName ?? '').isNotEmpty ? itemName : kDefaultItemName,
      qty: 1,
      // unit: '',
      unitPrice: price, // 單價 (總額)
      salesAmt: untaxPriceRound, // 銷售額 (未稅價)
      taxAmt: taxAmt,
      totalAmt: price,
      discountAmt: 0,
      healthAmt: 0,
      // relateNumber: '',
      // remark: '',
    );
    req.invoiceDetails ??= [item];
    final uri = Uri.parse('$baseUrl/PosInvoiceNews');
    // final uri = Uri.http('61.57.230.103', 'SCMWebAPITest/api/PosInvoiceNews');
    this.dio.options.contentType = 'application/json';
    final data = jsonEncode([req.toJson()]);
    logger.d(data);
    this.dio.options.contentType = Headers.jsonContentType;
    return this.dio.postUri<String>(uri, data: data).then(
      (value) {
        // logger.d('');
        final responseData = value.data;
        if (responseData != null) {
          final it = jsonDecode(responseData) as Iterable;
          final ls = it.map((e) => InvoiceCreated.fromJson(e));
          return Future.value(ls);
        }
        return Future.value(<InvoiceCreated>[]);
      },
      onError: (error) {
        logger.e(error);
        if (error is DioException) {
          final message = error.message ?? 'Unknown error';
          return Future<Iterable<InvoiceCreated>>.error(message);
        }
        return Future<Iterable<InvoiceCreated>>.error(error);
      },
    );
  }

  // Future<Iterable<InvoiceCreated>> _pushInvoice(InvoiceCreating value) {
  //   value.posBAN ??= kPosBAN;
  //   value.apiKey ??= kApiKey;
  //   value.state = 1;
  //   value.sellerBAN = kPosBAN;
  //   value.storeCode = '';
  //   value.storeName = '';
  //   value.registerCode = '';
  //   value.orderNo = '';
  //   value.buyerBAN = '';
  //   value.printMark = 'Y';
  //   value.memberId = '';
  //   value.checkNo = '';
  //   value.invoiceType = '35';
  //   value.groupMark = '';
  //   value.salesAmt = 100;
  //   value.freeTaxSalesAmt = 0;
  //   value.zeroTaxSalesAmt = 0;
  //   value.taxAmt = 0;
  //   value.totalAmt = 100;
  //   value.taxType = '1';
  //   value.taxRate = 0.05;
  //   value.discountAmt = 0;
  //   value.healthyAmt = 0;
  //   value.carrierType = '';
  //   value.carrierId1 = '';
  //   value.carrierId2 = '';
  //   value.npoBan = '';
  //   value.randomNumber ??= '1234';
  //   value.mainRemark = '';
  //   value.formatType = '';
  //   // value.invoiceDetails ??= '[${InvoiceDetails().toString()}]';
  //   final item = InvoiceDetails();
  //   // item.sequenceNo = '1';
  //   // item.itemName = 'a';
  //   // item.qty = 1;
  //   // item.unit = '';
  //   // item.unitPrice = 100;
  //   // item.salesAmt = 100;
  //   // item.taxAmt = 0;
  //   // item.totalAmt = 100;
  //   // item.discountAmt = 0;
  //   // item.healthAmt = 0;
  //   // item.relateNumber = '';
  //   // item.remark = '';
  //   // value.invoiceDetails ??= [item];
  //   value.invoiceDetails ??= [item];
  //   final uri = Uri.http('61.57.230.103', 'SCMWebAPITest/api/PosInvoiceNews');
  //   this.dio.options.contentType = 'application/json';
  //   final data = jsonEncode([value.toJson()]);
  //   kLogger.d(data);
  //   return this.dio.postUri<String>(uri, data: data).then(
  //     (value) {
  //       // kLogger.d('');
  //       final it = jsonDecode(value.data) as Iterable;
  //       final ls = it.map((e) => InvoiceCreated.fromJson(e));
  //       return Future.value(ls);
  //     },
  //     onError: (error) {
  //       kLogger.e(error);
  //       return Future<Iterable<InvoiceCreated>>.error(error);
  //     },
  //   );
  // }

  Future<String> getInvoiceNo({
    String? seller,
    required DateTime time,
  }) async {
    final uri = Uri.parse(soapUrl);
    dio.options.contentType = 'text/xml';
    final xmlString = _getSoapXmlString(
      year: time.year,
      month: time.month,
      machineNo: 'M1',
      seller: seller ?? '',
      main: posBAN,
      key: apiKey,
    );
    final res = await dio.postUri<String>(uri, data: xmlString);
    final responseData = res.data;
    if (responseData != null) {
      final doc = XmlDocument.parse(responseData);
      final it = doc.findAllElements('GetInvoiceRangeResult');
      if (it.length > 0) {
        final root = it.first;
        final result = root.findElements('Result').first.text;
        if ('OK' == result) {
          final invoiceNo = root.findElements('InvoiceStart').first.text;
          return invoiceNo;
        } else {
          final msg = root.findElements('ResultMsg').first.text;
          throw '加值中心: $msg';
        }
      }
    }
    throw 'xml parse error: GetInvoiceRangeResult';
  }

  ///
  /// TODO: try remove me
  /// 取得發票狀態
  ///
  // Future<String> getInvoiceStatus(Map data) {
  //   final uri = Uri.parse('$baseUrl/GetInvoiceStatus');
  //   // final uri =
  //   //     Uri.parse('https://www.bpscm.com.tw/SCMWebAPI/api/GetInvoiceStatus');
  //   return this.dio.postUri<Map>(uri, data: data).then(
  //     (value) {
  //       if (value.data.containsKey('Status')) {
  //         final message = value.data['Status'];
  //         return Future<String>.value(message);
  //       }
  //       return Future<String>.error('');
  //     },
  //     onError: (error, stackTrace) {
  //       return Future<String>.error(error, stackTrace);
  //     },
  //   );
  // }

  ///
  /// 取得發票狀態 (文字)
  ///
  Future<String> _getInvoiceStatus(InvoiceStatusReq data) async {
    final uri = Uri.parse('$baseUrl/GetInvoiceStatus');
    final json = data.toJson();
    final ret = await dio.postUri<Map>(uri, data: json);
    final responseData = ret.data;
    if (responseData != null && responseData.containsKey('Status')) {
      return responseData['Status'];
    }
    throw uri.path;
  }

  ///
  /// 取得發票狀態 (enum)
  ///
  Future<BpscmInvoiceStatus> getInvoiceStatus(InvoiceStatusReq data) async {
    final status = await _getInvoiceStatus(data);
    if (BpscmInvoiceStatus.Unknown.name == status) {
      return BpscmInvoiceStatus.Unknown;
    }
    if (BpscmInvoiceStatus.Invoice.name == status) {
      return BpscmInvoiceStatus.Invoice;
    }
    if (BpscmInvoiceStatus.Cancel.name == status) {
      return BpscmInvoiceStatus.Cancel;
    }
    return BpscmInvoiceStatus.Max;
  }

  ///
  /// 產生亂數
  ///
  static String createRandomNumber() {
    // HACK:
    // return '3361';
    final randomNumber = Random().nextInt(10000);
    return '000$randomNumber'.takeLast(4);
  }
}
