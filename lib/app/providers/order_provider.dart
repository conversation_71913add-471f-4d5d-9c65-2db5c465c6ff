import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:guests/app/models/order_Invoice_req.dart';
import 'package:guests/app/models/order_created_model.dart';
import 'package:guests/app/models/order_creating_model.dart';
import 'package:guests/app/models/order_detail_model.dart';
import 'package:guests/app/models/order_model.dart';
import 'package:guests/app/models/order_updated_ret.dart';
import 'package:guests/app/models/orders_req.dart';
import 'package:guests/app/providers/api_provider.dart';
import 'package:guests/app/providers/box_provider.dart';
import 'package:guests/app/providers/pref_provider.dart';
import 'package:guests/enums.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';
import 'package:guests/app/models/pagination_model.dart';

class Filter {
  String? keyword;
  DateTimeRange? dateTimeRange;
  final status = <num>[];
  String? text;
  // page
  num? _page;
  num get page => _page ?? 1;
  set page(num value) => _page = value;
  // limit
  num? _limit;
  num get limit => _limit ?? kLimit;
  set limit(num value) => _limit = value;
  num get offset => (page - 1) * limit;
  num get length => offset + limit;
  bool hasMore = false;

  bool validate(Order order) {
    if (status.isNotEmpty && !status.contains(order.status)) {
      return false;
    }
    // if (dateTimeRange != null && !dateTimeRange.contains(order.createdTime)) {
    //   return false;
    // }
    if (dateTimeRange != null) {
      if (order.createdTime?.isBefore(dateTimeRange!.start) == true) {
        return false;
      }
      if (order.createdTime?.isAfter(dateTimeRange!.end) == true) {
        return false;
      }
    }
    if (keyword != null && keyword!.isNotEmpty) {
      if (!order.match(keyword!)) {
        return false;
      }
    }
    return true;
  }
}

class OrderProvider {
  final ApiProvider apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;

  OrderProvider({
    required this.apiProvider,
  });

  Future<Pagination> getPagination(num limit) {
    final req = OrdersReq(
      page: 1,
      limit: limit,
    );
    return apiProvider.getRaw(
      unencodedPath: 'orders',
      filter: req.toJson(),
      creator: (json) => Pagination.fromJson(json['pagination']),
    );
  }

  Future<void> fetchAllOrders() async {
    kLogger.d('[OrderProvider] fetchAllOrders: 開始 (limit: $kLimit)');
    // 取得遠端訂單數量
    final pagi = await getPagination(kLimit);
    pagi.total ??= 0; // 避免 null
    pagi.lastPage ??= 1; // 避免 null
    // 取得本地訂單數量
    final box = await boxProvider.getLazyBox(BoxType.order);
    final total = pagi.total ?? 0;
    final lastPage = pagi.lastPage ?? 1;
    kLogger.d(
        '[OrderProvider] fetchAllOrders: 遠端訂單數量($total) 本地訂單數量(${box.length})');
    if (box.length < total) {
      // 續載訂單
      var page = 1 + box.length ~/ kLimit;
      do {
        kLogger.d('[OrderProvider] fetchAllOrders: 續載訂單 page($page)');
        await 1.seconds.delay();
        final orders = await getOrders(page: page, limit: kLimit);
        if (orders.length < kLimit) {
          break;
        }
        page++;
      } while (page <= lastPage);
      kLogger.d('[OrderProvider] fetchAllOrders: 續載訂單結束');
      // 新訂單
      page = 1;
      while (box.length < total) {
        kLogger.d('[OrderProvider] fetchAllOrders: 新訂單 page($page)');
        await 1.seconds.delay();
        await getOrders(page: page, limit: kLimit);
        page++;
        if (page > lastPage) {
          break;
        }
      }
      kLogger.d('[OrderProvider] fetchAllOrders: 新訂單結束');
    } else {
      kLogger.d('[OrderProvider] fetchAllOrders: 本地訂單數量已同步');
    }
    kLogger.d('[OrderProvider] fetchAllOrders: 完成');
  }

  Stream<Order> getOrdersWithIds(Iterable<num> ids) async* {
    final box = await boxProvider.getLazyBox(BoxType.order);
    for (var id in ids) {
      final jsonString = await box.get(id);
      yield Order.fromRawJson(jsonString);
    }
  }

  Future<Iterable<Order>> getOrders({
    num? page,
    num? limit,
    Filter? filter,
  }) async {
    final req = OrdersReq(
      page: page ?? 1,
      limit: limit ?? kLimit,
    );
    if (filter != null && filter.status.isNotEmpty) {
      req.status = filter.status.first;
    }
    final orders = await apiProvider.getData<Iterable<Order>>(
      unencodedPath: 'orders',
      filter: req.toJson(),
      creator: (json) {
        return List.from(json).map((e) => Order.fromJson(e));
      },
    );
    await _saveOrdersToBox(orders);
    return orders;
  }

  Future<void> _saveDetailToBox(OrderDetail orderDetail) async {
    final box = await boxProvider.getLazyBox(BoxType.orderDetail);
    await box.put(orderDetail.id, orderDetail.toRawJson());
  }

  Future<void> _saveOrdersToBox(Iterable<Order> it) async {
    final entries = it.map((e) => MapEntry(e.id, e.toRawJson()));
    final box = await boxProvider.getLazyBox(BoxType.order);
    await box.putAll(Map.fromEntries(entries));
  }

  Stream<Order> getOrderStreamFromLocalStorage(Filter filter) async* {
    final box = await boxProvider.getLazyBox(BoxType.order);
    final keys = List.from(box.keys, growable: false);
    // 由新到舊排序
    keys.sort((a, b) => b.compareTo(a));
    var validCount = 0;
    for (var i = 0; i < keys.length; i++) {
      final key = keys.elementAt(i);
      final val = await box.get(key);
      final order = Order.fromRawJson(val);
      if (filter.validate(order)) {
        validCount++;
        if (validCount <= filter.offset) {
          continue;
        }
        yield order;
        if (validCount >= filter.length) {
          break;
        }
      }
    }
  }

  ///
  /// 取得單一消費訂單資料
  ///
  Future<OrderDetail> getOrderDetail(String id) async {
    final orderDetail = await apiProvider.getData<OrderDetail>(
      unencodedPath: 'orders/$id',
      filter: {},
      creator: (json) => OrderDetail.fromJson(json),
    );
    await _saveDetailToBox(orderDetail);
    await _saveOrdersToBox([orderDetail.toOrder()]);
    return orderDetail;
  }

  // 已移除會員相關功能
  // Future<Iterable<Order>> getOrderWithMember(num id, [Filter filter]) async {
  //   final value = await apiProvider.getOrdersWithMember(id);
  //   if (filter != null) {
  //     return value.where(filter.validate);
  //   }
  //   return value;
  // }

  Future<num> createOrder(OrderCreating data) {
    // 找零必須有值且 >= 0
    final change = data.change ?? 0;
    if (change < 0) {
      data.paid = change * (-1);
      data.change = 0;
    }
    // disable invoice
    final temp = data.invoice;
    data.invoice = false;
    final dict = data.toJson();
    data.invoice = temp;
    return apiProvider.post<num>(
      'orders',
      data: dict,
      creator: (json) {
        final ret = OrderCreated.fromJson(json);
        final orderId = ret.orderId;
        if (orderId is num && orderId > 0) {
          return orderId;
        }
        return 0;
      },
    );
  }

  Future<num> createInvoice(num id, OrderInvoiceReq data) {
    return apiProvider.post<num>(
      'orders/$id/invoice',
      data: data.toJson(),
      creator: (json) {
        final ret = OrderUpdatedRet.fromJson(json);
        return ret.orderId ?? 0;
      },
    );
  }

  Future<num> refundOrder(String id) {
    return apiProvider.put<num>(
      'orders/$id/refund',
      creator: (json) {
        if (json.containsKey('order_id')) {
          return json['order_id'];
        }
        return 0;
      },
    );
  }
}
