import 'package:get_storage/get_storage.dart';
import 'package:guests/app/models/jwt_model.dart';
import 'package:guests/enums.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:path/path.dart' as p;
import 'package:hive/hive.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:logger/logger.dart';

class BoxProvider {
  final Logger logger;
  final Box userDefault;
  var _domain = '';

  set token(String? value) {
    if (value == null || value.isEmpty) {
      _domain = '';
    } else {
      final json = JwtDecoder.decode(value);
      final jwt = Jwt.fromJson(json);
      _domain = jwt.dir;
    }
    kLogger.d('[BoxProvider] set domain($_domain)');
  }

  BoxProvider(
    this.userDefault, {
    required this.logger,
  });

  Future<bool> initHiveLazyBox<E>(BoxType boxType) async {
    final name = boxType.value;
    final fullName = '$name.$_domain';
    if (Hive.isBoxOpen(fullName)) {
      return Future.value(true);
    }
    await Hive.openLazyBox<E>(fullName);
    return true; // Hive.openLazyBox never returns null in null-safe version
  }

  // Future<Box<E>> getBox<E>(String name) async {
  //   final fullName = '$name.$_domain';
  //   if (Hive.isBoxOpen(fullName)) {
  //     return Hive.box<E>(fullName);
  //   }
  //   // final appDir = await getApplicationDocumentsDirectory();
  //   // kLogger.d('[BoxProvider] getLazyBox($fullName) appDir: ${appDir.path}');
  //   // final dbDir = p.join(appDir.path, subDir);
  //   return Hive.openBox<E>(fullName);
  // }

  Future<LazyBox<E>> getLazyBox<E>(BoxType boxType) async {
    final name = boxType.value;
    final fullName = '$name.$_domain';
    if (Hive.isBoxOpen(fullName)) {
      return Hive.lazyBox<E>(fullName);
    }
    // final appDir = await getApplicationDocumentsDirectory();
    // kLogger.d('[BoxProvider] getLazyBox($fullName) appDir: ${appDir.path}');
    // kLogger.d('[BoxProvider] getLazyBox($name) appDir: ${appDir.path}');
    // final dbDir = p.join(appDir.path, subDir);
    return Hive.openLazyBox<E>(fullName);
  }

  // Box<E> getBoxSync<E>(String name) {
  //   final fullName = '$name.$_domain';
  //   return Hive.isBoxOpen(fullName) ? Hive.box<E>(fullName) : null;
  // }

  LazyBox<E>? getLazyBoxSync<E>(BoxType boxType) {
    final name = boxType.value;
    final fullName = '$name.$_domain';
    return Hive.isBoxOpen(fullName) ? Hive.lazyBox<E>(fullName) : null;
  }

  // Future<GetStorage> getGsBox(String name) async {
  //   final fullName = '$name.$_domain';
  //   kLogger.d('[BoxProvider] getGsBox($fullName)');
  //   final box = GetStorage(fullName);
  //   await box.initStorage;
  //   return box;
  // }

  Future<bool> initGsBox(BoxType boxType) {
    final name = boxType.value;
    final fullName = '$name.$_domain';
    return GetStorage.init(fullName);
  }

  GetStorage getGsBox(BoxType boxType) {
    final name = boxType.value;
    final fullName = '$name.$_domain';
    kLogger.d('[BoxProvider] getGsBox($fullName)');
    return GetStorage(fullName);
  }
}
