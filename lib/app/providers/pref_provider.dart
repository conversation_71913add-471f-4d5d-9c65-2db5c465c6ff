import 'package:get/get.dart';
import 'package:guests/app/models/brands_info_model.dart';
import 'package:guests/app/models/channels_info_model.dart';
import 'package:guests/app/models/jwt_model.dart';
import 'package:guests/app/models/store_account_model.dart';
import 'package:guests/app/providers/box_provider.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';
import 'package:guests/keys.dart';
import 'package:guests/app/models/client_info_model.dart';
import 'package:hive/hive.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:logger/logger.dart';
import 'package:package_info_plus/package_info_plus.dart';

class PrefProvider {
  static const _hostDev = 'dev-api-pos.omos.tw';
  static const _hostProd = 'api-pos.omos.tw';
  static const _invoiceApiKeyDev = 'S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY';
  static const _invoiceApiKeyProd = 'S6FR5FQE-S6FR-S6FR-S6FR-S6FR5FQE2SMA';
  static const _invoiceApiUrlDev = 'https://webtest.bpscm.com.tw/SCMWEBAPI/API';
  static const _invoiceApiUrlProd = 'https://www.bpscm.com.tw/SCMWebAPI/api';
  static const _soapUrlDev =
      'https://webtest.bpscm.com.tw/WebService_Npoi2/PosInvoiceRange.asmx?WSDL';
  static const _soapUrlProd =
      'https://www.bpscm.com.tw/WebService_Npoi2/PosInvoiceRange.asmx?WSDL';

  Box get userDefault => boxProvider.userDefault;
  // Invoice settings
  bool get invoiceEnabled => userDefault.get(Keys.InvoiceEnabled, defaultValue: false);
  set invoiceEnabled(bool value) => userDefault.put(Keys.InvoiceEnabled, value);

  bool get invoiceSkipped => userDefault.get(Keys.InvoiceSkipped, defaultValue: false);
  set invoiceSkipped(bool value) => userDefault.put(Keys.InvoiceSkipped, value);
  final PackageInfo packageInfo;
  final Logger logger;
  // Item settings
  String get itemName => userDefault.get(Keys.ItemName, defaultValue: kDefaultItemName);
  set itemName(String value) => userDefault.put(Keys.ItemName, value);
  
  num get taxType => userDefault.get(Keys.TaxType, defaultValue: 1);
  set taxType(num value) => userDefault.put(Keys.TaxType, value);
  // token
  bool get isLogin {
    if (token.isEmpty) {
      return false;
    }
    if (jwt.isExpired) {
      return false;
    }
    if (jwt.isTemporary) {
      return false;
    }
    return true;
  }
  bool get isLogout => !isLogin;
  String get token => userDefault.get(Keys.Token, defaultValue: '');
  set token(String? value) {
    value ??= '';
    if (token != value) {
      logger.d('[PrefProvider] set token: $value');
      // 先設定 token 到 boxProvider 產生 domain
      boxProvider.token = value;
      userDefault.put(Keys.Token, value);
    }
  }

  final BoxProvider boxProvider;

  PrefProvider({
    required this.packageInfo,
    required this.logger,
    required this.boxProvider,
  });

  // ValueListenable<Box> getListenalbe({Iterable keys}) {
  //   return box.listenable(keys: keys);
  // }

  bool get isDevelopment {
    final packageName = packageInfo.packageName;
    return GetUtils.hasMatch(packageName, r'\.dev');
  }

  String get host {
    return isDevelopment ? _hostDev : _hostProd;
  }

  String get invoiceApiKey {
    return isDevelopment ? _invoiceApiKeyDev : _invoiceApiKeyProd;
  }

  String get invoiceApiUrl {
    return isDevelopment ? _invoiceApiUrlDev : _invoiceApiUrlProd;
  }

  String get invoiceSoapUrl {
    return isDevelopment ? _soapUrlDev : _soapUrlProd;
  }

  // String get itemName {
  //   return this.box.get(kKeyItemName, defaultValue: kDefaultItemName);
  // }

  // set itemName(String value) {
  //   this.box.put(kKeyItemName, value);
  // }

  // num get taxType {
  //   return this.box.get(kKeyTaxType, defaultValue: 1);
  // }

  // set taxType(num value) {
  //   this.box.put(kKeyTaxType, value);
  // }

  Jwt get jwt {
    // final a = JwtDecoder.getExpirationDate(this.token);
    // final b = JwtDecoder.getRemainingTime(this.token);
    // final c = JwtDecoder.getTokenTime(this.token);
    // final d = JwtDecoder.isExpired(this.token);
    // final json = JwtDecoder.decode(this.token);
    final json = JwtDecoder.decode(token);
    return Jwt.fromJson(json);
  }

  BrandsInfo get brandsInfo {
    final jsonString = userDefault.get(Keys.BrandsInfo, defaultValue: '{}');
    return BrandsInfo.fromRawJson(jsonString);
  }

  set brandsInfo(BrandsInfo value) {
    final jsonString = value.toRawJson();
    userDefault.put(Keys.BrandsInfo, jsonString);
  }

  ChannelsInfo get channelsInfo {
    final jsonString = userDefault.get(Keys.ChannelsInfo, defaultValue: '{}');
    return ChannelsInfo.fromRawJson(jsonString);
  }

  set channelsInfo(ChannelsInfo value) {
    final jsonString = value.toRawJson();
    userDefault.put(Keys.ChannelsInfo, jsonString);
  }

  set clientInfo(ClientInfo value) {
    final jsonString = value.toRawJson();
    userDefault.put(Keys.ClientInfo, jsonString);
  }

  StoreAccount get loginAccount {
    final jsonString = userDefault.get(Keys.LoginUser, defaultValue: '{}');
    return StoreAccount.fromRawJson(jsonString);
  }

  set loginAccount(StoreAccount value) {
    final jsonString = value.toRawJson();
    userDefault.put(Keys.LoginUser, jsonString);
  }

  String get storeName {
    // HACK:
    // return '洄瀾禮好有限公司';
    return brandsInfo.name ?? '';
  }

  String get taxId {
    // HACK:
    // return '********';
    final cTaxId = channelsInfo.otherObject.taxId ?? '';
    if (cTaxId.isNotEmpty) {
      return cTaxId;
    }
    return brandsInfo.taxId ?? '';
  }
}
